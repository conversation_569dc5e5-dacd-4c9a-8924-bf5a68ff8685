body {
    font-family: 'Arial', sans-serif;
    background-color: #f0f4f8;
    margin: 0;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.dashboard {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    width: 100%;
    max-width: 600px; /* Limits width on larger screens */
}

.card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;
    width: 100%;
    text-align: center;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px); /* Slight lift on hover, disabled on mobile */
}

.header-card h1 {
    color: #2c3e50;
    font-size: 28px;
    margin: 0;
}

.status-card h2, .controls-card h2, .log-card h2 {
    color: #34495e;
    font-size: 20px;
    margin-bottom: 10px;
}

.status-card p {
    font-size: 16px;
    color: #7f8c8d;
    margin: 0;
}

#status {
    font-weight: bold;
    color: #3498db;
}

.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap; /* Allows buttons to stack on small screens */
}

.btn {
    padding: 10px 25px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s, transform 0.2s;
    width: 120px; /* Fixed width for consistency */
}

.btn-start {
    background: #2ecc71;
    color: #fff;
}

.btn-start:hover {
    background: #27ae60;
    transform: scale(1.05);
}

.btn-stop {
    background: #e74c3c;
    color: #fff;
}

.btn-stop:hover {
    background: #c0392b;
    transform: scale(1.05);
}

.log-card {
    max-height: 400px;
}

.log-list {
    background: #ecf0f1;
    padding: 15px;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 14px;
    color: #2c3e50;
    text-align: left;
}

.log-list div {
    padding: 8px 0;
    border-bottom: 1px solid #dfe6e9;
    word-wrap: break-word;
}

.log-list div:last-child {
    border-bottom: none;
}

.log-list::-webkit-scrollbar {
    width: 8px;
}

.log-list::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 4px;
}

.log-list::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard {
        padding: 10px;
        max-width: 100%; /* Full width on mobile */
    }

    .card {
        padding: 15px;
        margin: 0 10px; /* Small margin for edge spacing */
    }

    .header-card h1 {
        font-size: 24px; /* Smaller title */
    }

    .status-card h2, .controls-card h2, .log-card h2 {
        font-size: 18px; /* Smaller headings */
    }

    .status-card p {
        font-size: 14px;
    }

    .controls {
        flex-direction: column; /* Stack buttons vertically */
        gap: 10px;
    }

    .btn {
        width: 100%; /* Full-width buttons */
        padding: 12px;
        font-size: 14px;
    }

    .btn:hover {
        transform: none; /* Disable hover effect on mobile */
    }

    .card:hover {
        transform: none; /* Disable card lift on mobile */
    }

    .log-card {
        max-height: 300px; /* Reduce height for smaller screens */
    }

    .log-list {
        max-height: 200px;
        font-size: 12px; /* Smaller text for logs */
    }
}

@media (max-width: 480px) {
    .header-card h1 {
        font-size: 20px;
    }

    .status-card h2, .controls-card h2, .log-card h2 {
        font-size: 16px;
    }

    .status-card p {
        font-size: 12px;
    }

    .btn {
        font-size: 12px;
        padding: 10px;
    }

    .log-list {
        max-height: 150px; /* Even smaller log area */
    }
}