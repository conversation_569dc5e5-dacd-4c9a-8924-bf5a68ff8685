"""
<PERSON><PERSON><PERSON> to create a database migration for changing the picture column to Text.
"""
from app import app
from models import db
from flask_migrate import Migra<PERSON>, migrate, upgrade

def create_migration():
    """Create a migration to change the picture column to Text"""
    print("Creating migration for changing picture column to Text...")
    
    # Initialize Flask-Migrate
    migrate_instance = Migrate(app, db)
    
    with app.app_context():
        # Create a migration
        migrate(message="Change picture column to Text")
        print("Migration created")
        
        # Apply the migration
        upgrade()
        print("Migration applied")
        
        print("Migration completed successfully!")

if __name__ == "__main__":
    create_migration()
