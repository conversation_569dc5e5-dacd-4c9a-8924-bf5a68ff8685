# Render PostgreSQL Database Viewer

This tool provides a simple way to view and query data stored in your Render PostgreSQL database without needing to install additional database management tools.

## Prerequisites

1. Make sure your `.env` file contains the `DATABASE_URL` environment variable with your Render PostgreSQL connection string.
2. Install the required Python packages:
   ```
   pip install psycopg2-binary tabulate python-dotenv
   ```

## Usage

### Using the Batch Script (Windows)

The `view_db.bat` script provides easy access to common database operations:

1. **List all tables**:
   ```
   view_db tables
   ```

2. **View records in a table**:
   ```
   view_db view user
   ```
   
   With custom limit:
   ```
   view_db view user 10
   ```

3. **Count records in all tables**:
   ```
   view_db count
   ```

4. **Execute a custom SQL query**:
   ```
   view_db query "SELECT * FROM user WHERE email LIKE '%example.com%'"
   ```

5. **Show help**:
   ```
   view_db help
   ```

### Using the Python Script Directly

You can also use the Python script directly:

1. **List all tables**:
   ```
   python view_database.py list_tables
   ```

2. **View records in a table**:
   ```
   python view_database.py view_table user
   ```
   
   With custom limit:
   ```
   python view_database.py view_table user 10
   ```

3. **Count records in all tables**:
   ```
   python view_database.py count_records
   ```

4. **Execute a custom SQL query**:
   ```
   python view_database.py query "SELECT * FROM user WHERE email LIKE '%example.com%'"
   ```

5. **Show help**:
   ```
   python view_database.py help
   ```

## Common Queries

Here are some useful queries you can run:

1. **View all users**:
   ```
   view_db query "SELECT id, email, name FROM user"
   ```

2. **Check user settings**:
   ```
   view_db query "SELECT * FROM user_settings"
   ```

3. **View recent email replies**:
   ```
   view_db query "SELECT * FROM email_reply ORDER BY created_at DESC LIMIT 10"
   ```

4. **Count emails by status**:
   ```
   view_db query "SELECT status, COUNT(*) FROM email_reply GROUP BY status"
   ```

## Troubleshooting

If you encounter any issues:

1. **Connection problems**: Make sure your `DATABASE_URL` is correct in the `.env` file and includes the proper credentials.

2. **Missing packages**: Run `pip install psycopg2-binary tabulate python-dotenv` to install required packages.

3. **Permission issues**: Ensure your database user has the necessary permissions to view the tables you're trying to access.

4. **SSL errors**: If you get SSL-related errors, make sure your connection string includes `?sslmode=require` at the end.
