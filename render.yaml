# Using existing Render PostgreSQL database
# Database: emailagent
# Hostname: dpg-d06k3ek9c44c73fkgtb0-a
# Username: emailagent_user

services:
  - type: web
    name: email-agent
    env: python
    buildCommand: pip install -r requirements.txt && python update_psycopg2.py && python test_ssl_connection.py && python verify_db_connection.py && python migrate_db_production.py
    startCommand: gunicorn app:app --workers 2 --worker-class uvicorn.workers.UvicornWorker --timeout 120 --bind 0.0.0.0:$PORT
    plan: starter
    healthCheckPath: /health
    autoDeploy: true
    domains:
      - emailagentprod.onrender.com
    envVars:
      - key: PYTHON_VERSION
        value: 3.12.0
      # Environment flags
      - key: FLASK_ENV
        value: production
      - key: RENDER
        value: true
      - key: FLASK_APP
        value: app.py
      - key: FLASK_DEBUG
        value: false
      # Security settings
      - key: HTTPS_ENABLED
        value: true
      - key: BASE_URL
        value: https://emailagentprod.onrender.com
      # Sensitive credentials (stored securely in Render dashboard)
      - key: FLASK_SECRET_KEY
        sync: false
      - key: GOOGLE_CLIENT_ID
        sync: false
      - key: GOOGLE_CLIENT_SECRET
        sync: false
      - key: OPENROUTER_API_KEY
        sync: false
      # Database configuration
      - key: DATABASE_URL
        value: postgresql://emailagent_user:<EMAIL>/emailagent
    scaling:
      minInstances: 1
      maxInstances: 3
      targetMemoryPercent: 80
      targetCPUPercent: 80
