"""
<PERSON><PERSON><PERSON> to create a database migration for changing the UserSettings and UserStats models.
"""
from app import app
from models import db
from flask_migrate import Migrate, migrate, upgrade

def create_migration():
    """Create a migration to change the UserSettings and UserStats models"""
    print("Creating migration for changing UserSettings and UserStats models...")
    
    # Initialize Flask-Migrate
    migrate_instance = Migrate(app, db)
    
    with app.app_context():
        # Create a migration
        migrate(message="Change UserSettings and UserStats models to use user_id as primary key")
        print("Migration created")
        
        # Apply the migration
        upgrade()
        print("Migration applied")
        
        print("Migration completed successfully!")

if __name__ == "__main__":
    create_migration()
