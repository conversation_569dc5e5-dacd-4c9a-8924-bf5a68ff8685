"""
Migration script to help transition from SQLite to Render PostgreSQL.
This script will initialize the database tables in PostgreSQL.
"""
from app import app
from models import db
from flask_migrate import Migrate, init, migrate, upgrade
import os
import time
import sys

def migrate_to_postgres():
    """Initialize and migrate the database to PostgreSQL"""
    print("Starting migration to Render PostgreSQL...")

    # Make sure we're using the PostgreSQL database
    database_url = app.config['SQLALCHEMY_DATABASE_URI']
    if 'postgresql://' not in database_url:
        print("Error: DATABASE_URL environment variable not set or not a PostgreSQL URL")
        print("Current database URI:", database_url)
        print("Please set the DATABASE_URL environment variable to your Render PostgreSQL URL")
        return False

    print(f"Using database URL: {database_url.replace(database_url.split('@')[0], 'postgresql://****:****')}")

    # Add retry mechanism for database connection
    max_retries = 5
    retry_delay = 2  # seconds

    for attempt in range(1, max_retries + 1):
        try:
            print(f"Connection attempt {attempt} of {max_retries}...")

            with app.app_context():
                # Check database connection first
                from sqlalchemy import text
                db.session.execute(text('SELECT 1'))
                db.session.commit()
                print("Database connection successful!")

                # Initialize migrations if not already initialized
                try:
                    init()
                    print("Migrations initialized")
                except Exception as init_error:
                    print(f"Note: {init_error}")
                    print("Migrations may already be initialized, continuing...")

                # Create a migration
                try:
                    migrate(message="Migrate to Render PostgreSQL")
                    print("Migration created")
                except Exception as migrate_error:
                    print(f"Note: {migrate_error}")
                    print("Continuing with upgrade...")

                # Apply the migration
                upgrade()
                print("Migration applied")

                # Create tables if they don't exist
                db.create_all()
                print("Tables created/verified")

                # Verify database connection again
                db.session.execute(text('SELECT version()'))
                db.session.commit()
                print("Database setup verified")

                print("Migration to Render PostgreSQL completed successfully!")
                return True

        except Exception as e:
            print(f"Error during migration attempt {attempt}: {e}")
            if attempt < max_retries:
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                print("All migration attempts failed.")
                return False

if __name__ == "__main__":
    success = migrate_to_postgres()
    if not success:
        sys.exit(1)  # Exit with error code for CI/CD pipelines
