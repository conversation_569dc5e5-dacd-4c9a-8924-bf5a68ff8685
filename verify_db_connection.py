"""
Simple script to verify database connection.
This can be run during the build process to ensure the database is accessible.
"""
import os
import sys
import time
import psycopg2

# Force production environment
os.environ['FLASK_ENV'] = 'production'
os.environ['RENDER'] = 'true'

def verify_database_connection():
    """Verify that we can connect to the PostgreSQL database"""
    print("Verifying PostgreSQL database connection...")

    # Get database URL from environment
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        return False

    # Hide password in logs
    masked_url = database_url
    if '@' in database_url:
        parts = database_url.split('@')
        auth_part = parts[0].split('://')
        if len(auth_part) > 1:
            user_pass = auth_part[1].split(':')
            if len(user_pass) > 1:
                masked_url = f"{auth_part[0]}://{user_pass[0]}:****@{parts[1]}"

    print(f"Using database URL: {masked_url}")

    # Try to connect with retries
    max_retries = 5
    retry_delay = 5  # seconds

    for attempt in range(1, max_retries + 1):
        try:
            print(f"Connection attempt {attempt} of {max_retries}...")

            # Enhanced connection handling for PostgreSQL
            conn_params = {}

            # Parse the connection string
            if '?' in database_url:
                # Extract the base connection string
                conn_string = database_url.split('?')[0]

                # Parse additional parameters
                params_str = database_url.split('?')[1]
                for param in params_str.split('&'):
                    if '=' in param:
                        key, value = param.split('=')
                        conn_params[key] = value
            else:
                conn_string = database_url

            # Ensure SSL is enabled for PostgreSQL
            if conn_string.startswith('postgresql://'):
                conn_params['sslmode'] = 'require'
                conn_params['application_name'] = 'email_agent_verify'
                conn_params['connect_timeout'] = '10'

                # Add additional SSL parameters to improve connection reliability
                if 'sslrootcert' not in conn_params:
                    conn_params['sslrootcert'] = 'none'

                print(f"Using PostgreSQL with SSL and parameters: {conn_params}")

            # Connect to the database with enhanced parameters
            conn = psycopg2.connect(conn_string, **conn_params)

            # Create a cursor
            cur = conn.cursor()

            # Execute a simple query
            cur.execute('SELECT version();')

            # Fetch the result
            version = cur.fetchone()
            print(f"PostgreSQL version: {version[0]}")

            # Close the cursor and connection
            cur.close()
            conn.close()

            print("Database connection successful!")
            return True

        except Exception as e:
            print(f"Error connecting to database: {e}")
            if attempt < max_retries:
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                # Increase delay for next attempt
                retry_delay *= 1.5
            else:
                print("Maximum retry attempts reached. Database connection failed.")
                return False

if __name__ == "__main__":
    success = verify_database_connection()
    if not success:
        sys.exit(1)  # Exit with error code
    sys.exit(0)  # Exit with success code
