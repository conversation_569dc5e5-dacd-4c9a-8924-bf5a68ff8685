document.addEventListener('DOMContentLoaded', () => {
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const statusSpan = document.getElementById('status');
    const logList = document.getElementById('logList');

    // Fetch and update status
    function updateStatus() {
        fetch('/status')
            .then(response => response.json())
            .then(data => {
                statusSpan.textContent = data.running ? 'Running' : 'Stopped';
                statusSpan.style.color = data.running ? '#2ecc71' : '#e74c3c';
            })
            .catch(error => console.error('Error fetching status:', error));
    }

    // Fetch and update logs
    function updateLogs() {
        fetch('/logs')
            .then(response => response.json())
            .then(data => {
                logList.innerHTML = ''; // Clear existing logs
                data.logs.forEach(log => {
                    const logItem = document.createElement('div');
                    logItem.textContent = log;
                    logList.appendChild(logItem);
                });
                logList.scrollTop = logList.scrollHeight; // Auto-scroll to bottom
            })
            .catch(error => console.error('Error fetching logs:', error));
    }

    // Start agent
    startBtn.addEventListener('click', () => {
        fetch('/start', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                alert(data.status);
                updateStatus();
            })
            .catch(error => console.error('Error starting agent:', error));
    });

    // Stop agent
    stopBtn.addEventListener('click', () => {
        fetch('/stop', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                alert(data.status);
                updateStatus();
            })
            .catch(error => console.error('Error stopping agent:', error));
    });

    // Initial updates
    updateStatus();
    updateLogs();

    // Periodic updates
    setInterval(updateStatus, 5000); // Status every 5 seconds
    setInterval(updateLogs, 2000);   // Logs every 2 seconds
});