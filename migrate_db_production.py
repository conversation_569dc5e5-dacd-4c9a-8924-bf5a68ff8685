"""
Production database migration script for Render PostgreSQL.
This script handles the migration from SQLite to PostgreSQL in a production environment.
"""
import os
import time
import sys

# Force production environment
os.environ['FLASK_ENV'] = 'production'
os.environ['RENDER'] = 'true'

# Import app and models after setting environment variables
from app import app
from models import db
from flask_migrate import Migrate, init, migrate, upgrade

def migrate_to_postgres():
    """Initialize and migrate the database to PostgreSQL"""
    print("Starting production migration to PostgreSQL...")

    # Make sure we're using the PostgreSQL database
    database_url = os.getenv('DATABASE_URL', '')
    if not database_url.startswith('postgresql://'):
        print("Error: DATABASE_URL environment variable not set or not a PostgreSQL URL")
        print("Current database URI:", database_url)
        print("Please set the DATABASE_URL environment variable to your Render PostgreSQL URL")
        return False

    # Add SSL mode if not present
    if '?sslmode=' not in database_url:
        database_url += '?sslmode=require'
        os.environ['DATABASE_URL'] = database_url
        print("Added sslmode=require to DATABASE_URL")

    # Set the database URI in the app config
    app.config['SQLALCHEMY_DATABASE_URI'] = database_url

    # Get pool settings from config
    from config import active_config as config

    # Add connection pool settings
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_size': config.DB_POOL_SIZE,
        'max_overflow': config.DB_MAX_OVERFLOW,
        'pool_timeout': config.DB_POOL_TIMEOUT,
        'pool_recycle': config.DB_POOL_RECYCLE,
    }

    try:
        with app.app_context():
            # First, check if we can connect to the database
            print("Testing database connection...")
            try:
                from sqlalchemy import text
                db.session.execute(text('SELECT 1'))
                db.session.commit()
                print("Database connection successful!")
            except Exception as e:
                print(f"Error connecting to database: {e}")
                print("Please check your DATABASE_URL and make sure the database is accessible")
                return False

            # Initialize migrations if not already initialized
            print("Initializing migrations...")
            try:
                init()
                print("Migrations initialized")
            except Exception as e:
                print(f"Note: {e}")
                print("Migrations may already be initialized, continuing...")

            # Create a migration
            print("Creating migration...")
            try:
                migrate(message="Migrate to Render PostgreSQL")
                print("Migration created")
            except Exception as e:
                print(f"Error creating migration: {e}")
                print("Trying to continue with upgrade...")

            # Apply the migration
            print("Applying migration...")
            try:
                upgrade()
                print("Migration applied")
            except Exception as e:
                print(f"Error applying migration: {e}")
                print("Trying to create tables directly...")

                # If migration fails, try to create tables directly
                try:
                    db.create_all()
                    print("Tables created directly")
                except Exception as e2:
                    print(f"Error creating tables: {e2}")
                    return False

            # Verify database connection and tables
            print("Verifying database setup...")
            try:
                # Check if User table exists and is accessible
                from models import User
                user_count = User.query.count()
                print(f"Database verification successful! Found {user_count} users.")

                # Get PostgreSQL version
                version_result = db.session.execute(text('SELECT version()'))
                version = version_result.scalar()
                print(f"PostgreSQL version: {version}")

                print("Migration to Render PostgreSQL completed successfully!")
                return True
            except Exception as e:
                print(f"Error verifying database: {e}")
                return False
    except Exception as e:
        print(f"Unexpected error during migration: {e}")
        return False

if __name__ == "__main__":
    # Add a retry mechanism for production environments
    max_retries = 5
    retry_delay = 5  # seconds

    for attempt in range(1, max_retries + 1):
        print(f"Migration attempt {attempt} of {max_retries}")
        success = migrate_to_postgres()

        if success:
            print("Migration successful!")
            sys.exit(0)

        if attempt < max_retries:
            print(f"Migration failed. Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
            retry_delay *= 2  # Exponential backoff
        else:
            print("All migration attempts failed. Please check your database configuration.")
            sys.exit(1)
