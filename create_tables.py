from app import app
from models import db
from sqlalchemy import text

with app.app_context():
    try:
        # Check database connection using SQLAlchemy 2.0 API
        db.session.execute(text('SELECT 1'))
        db.session.commit()
        print("Database connection successful")

        # Create all tables
        db.create_all()
        print("Database tables created successfully!")
    except Exception as e:
        print(f"Database connection error: {e}")
        print("Warning: Application may not function correctly without database access")
