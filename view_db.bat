@echo off
REM Database Viewer Helper Script
REM This script makes it easier to run common database viewing commands

if "%1"=="" goto help
if "%1"=="help" goto help
if "%1"=="tables" goto tables
if "%1"=="view" goto view
if "%1"=="count" goto count
if "%1"=="query" goto query

:help
echo.
echo Database Viewer Helper
echo =====================
echo.
echo Usage:
echo   view_db help                - Show this help message
echo   view_db tables              - List all tables in the database
echo   view_db view [table] [limit]- View records in a table (default limit: 100)
echo   view_db count               - Count records in all tables
echo   view_db query "SQL QUERY"   - Execute a custom SQL query
echo.
goto end

:tables
python view_database.py list_tables
goto end

:view
if "%2"=="" (
    echo Error: Missing table name
    echo Usage: view_db view [table] [limit]
    goto end
)
if "%3"=="" (
    python view_database.py view_table %2
) else (
    python view_database.py view_table %2 %3
)
goto end

:count
python view_database.py count_records
goto end

:query
if "%~2"=="" (
    echo Error: Missing SQL query
    echo Usage: view_db query "SQL QUERY"
    goto end
)
python view_database.py query "%~2"
goto end

:end
