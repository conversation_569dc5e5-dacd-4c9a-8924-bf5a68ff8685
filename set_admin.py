"""
<PERSON><PERSON>t to set a user as an admin.
Usage: python set_admin.py <email>
"""
import os
import sys
import time
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import User, db

# Load environment variables
load_dotenv()

def set_user_as_admin(email):
    """Set a user as an admin by email"""
    with app.app_context():
        # Find the user by email
        user = User.query.filter_by(email=email).first()

        if not user:
            print(f"Error: User with email '{email}' not found")
            print("Creating a placeholder user with admin privileges...")

            # Create a placeholder user with admin privileges
            # The user will need to sign in with Google to complete their profile
            user_id = f"admin_{int(time.time())}"  # Generate a temporary ID
            user = User(user_id, email, "Admin User", "", is_admin=True)
            db.session.add(user)
            db.session.commit()

            print(f"Created placeholder admin user with email '{email}'")
            print("Note: This user will need to sign in with Google to complete their profile")
            return True

        # Set the user as an admin
        if user.is_administrator():
            print(f"User '{user.name}' ({user.email}) is already an admin")
            return True

        User.set_admin_status(user.id, True)
        print(f"User '{user.name}' ({user.email}) has been set as an admin")
        return True

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python set_admin.py <email>")
        sys.exit(1)

    email = sys.argv[1]
    success = set_user_as_admin(email)

    if not success:
        sys.exit(1)
