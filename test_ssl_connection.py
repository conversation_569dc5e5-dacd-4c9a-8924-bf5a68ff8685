"""
Test script to verify SSL connection to Render PostgreSQL database.
This script runs automatically during the Render build process.
"""
import os
import sys
import time
import psycopg2
from dotenv import load_dotenv

# Load environment variables (for local development)
try:
    load_dotenv()
except ImportError:
    print("dotenv not installed, skipping .env file loading")

def test_ssl_connection():
    """Test SSL connection to the Render PostgreSQL database"""
    print("=" * 80)
    print("SSL CONNECTION TEST")
    print("=" * 80)

    # Check if we're in a Render environment
    is_render = os.environ.get('RENDER') == 'true'
    if is_render:
        print("Running in Render production environment")
    else:
        print("Running in local/development environment")

    # Get database URL from environment variable
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        print("Please set the DATABASE_URL environment variable to your Render PostgreSQL URL")
        return False

    # Hide password in logs
    masked_url = database_url
    if '@' in database_url:
        parts = database_url.split('@')
        auth_part = parts[0].split('://')
        if len(auth_part) > 1:
            user_pass = auth_part[1].split(':')
            if len(user_pass) > 1:
                masked_url = f"{auth_part[0]}://{user_pass[0]}:****@{parts[1]}"

    print(f"Using database URL: {masked_url}")

    # Enhanced connection handling for PostgreSQL
    conn_params = {}

    # Parse the connection string
    if '?' in database_url:
        # Extract the base connection string
        conn_string = database_url.split('?')[0]

        # Parse additional parameters
        params_str = database_url.split('?')[1]
        for param in params_str.split('&'):
            if '=' in param:
                key, value = param.split('=')
                conn_params[key] = value
    else:
        conn_string = database_url

    # Ensure SSL is enabled for PostgreSQL
    if conn_string.startswith('postgresql://'):
        conn_params['sslmode'] = 'require'
        conn_params['application_name'] = 'email_agent_ssl_test'
        conn_params['connect_timeout'] = '10'

        # Add additional SSL parameters to improve connection reliability
        if 'sslrootcert' not in conn_params:
            conn_params['sslrootcert'] = 'none'

        print(f"Using PostgreSQL with SSL and parameters: {conn_params}")

    # Try with retries
    max_retries = 3
    retry_delay = 5  # seconds

    for attempt in range(1, max_retries + 1):
        try:
            print(f"Connection attempt {attempt} of {max_retries}...")

            # Connect to the database with enhanced parameters
            print("Connecting to database...")
            conn = psycopg2.connect(conn_string, **conn_params)

            # Create a cursor
            cur = conn.cursor()

            # Execute a simple query
            print("Executing query...")
            cur.execute('SELECT version();')

            # Fetch the result
            version = cur.fetchone()
            print(f"PostgreSQL version: {version[0]}")

            # Get database size
            cur.execute("SELECT pg_size_pretty(pg_database_size(current_database()));")
            size = cur.fetchone()
            print(f"Database size: {size[0]}")

            # Get table count
            cur.execute("SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
            table_count = cur.fetchone()
            print(f"Number of tables: {table_count[0]}")

            # Get SSL info
            cur.execute("SHOW ssl;")
            ssl_status = cur.fetchone()
            print(f"SSL enabled: {ssl_status[0]}")

            # Close the cursor and connection
            cur.close()
            conn.close()

            print("=" * 80)
            print("SSL CONNECTION TEST SUCCESSFUL")
            print("=" * 80)
            return True

        except Exception as e:
            print(f"SSL connection attempt {attempt} failed: {e}")
            if attempt < max_retries:
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                print("All connection attempts failed.")
                print("=" * 80)
                print("SSL CONNECTION TEST FAILED")
                print("=" * 80)
                return False

if __name__ == "__main__":
    success = test_ssl_connection()
    if not success:
        sys.exit(1)  # Exit with error code
