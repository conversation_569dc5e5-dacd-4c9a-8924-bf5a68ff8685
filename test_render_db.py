"""
Test script to verify connection to Render PostgreSQL database.
"""
import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_render_db_connection():
    """Test connection to the Render PostgreSQL database"""
    print("Testing connection to Render PostgreSQL database...")
    
    # Get database URL from environment variable
    database_url = os.getenv('DATABASE_URL')
    
    # Hide password in output
    display_url = database_url.replace(database_url.split(':')[2].split('@')[0], '********')
    print(f"Using DATABASE_URL: {display_url}")
    
    try:
        # Connect to the database
        conn = psycopg2.connect(database_url)
        
        # Create a cursor
        cur = conn.cursor()
        
        # Execute a simple query
        cur.execute('SELECT version();')
        
        # Fetch the result
        version = cur.fetchone()
        print(f"PostgreSQL version: {version[0]}")
        
        # Get database size
        cur.execute("SELECT pg_size_pretty(pg_database_size(current_database()));")
        size = cur.fetchone()
        print(f"Database size: {size[0]}")
        
        # Get table count
        cur.execute("SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
        table_count = cur.fetchone()
        print(f"Number of tables: {table_count[0]}")
        
        # Close the cursor and connection
        cur.close()
        conn.close()
        
        print("Connection successful!")
        return True
    except Exception as e:
        print(f"Connection failed: {e}")
        return False

if __name__ == "__main__":
    test_render_db_connection()
