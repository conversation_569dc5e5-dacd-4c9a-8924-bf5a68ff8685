/* Unified Dashboard CSS - Aligned with <PERSON> Page and Auth Pages Design */
:root {
  /* Color Palette - Matching Requirements */
  --primary: #1e90ff; /* Primary blue */
  --primary-dark: #0066cc; /* Darker blue for contrast */
  --primary-light: #5cacff; /* Lighter blue for accents */
  --secondary: #ff5733; /* Secondary orange/red */
  --accent: #28a745; /* Accent green */
  --neutral: #f5f5f5; /* Neutral light gray */
  --light: #f5f5f5; /* Light gray for backgrounds */
  --dark: #333333; /* Dark gray for text - better contrast */
  --white: #ffffff;
  --gray: #6c757d; /* Medium gray for secondary text */
  --gray-light: #e9ecef;
  --success: #28a745;
  --danger: #ff5733;
  --warning: #ffc107;
  --info: #17a2b8;
  --border-color: #e0e0e0;

  /* Gradients */
  --gradient-bg: linear-gradient(135deg, #333333 0%, #1e90ff 100%);
  --gradient-btn-primary: linear-gradient(135deg, #1e90ff 0%, #0066cc 100%);
  --gradient-btn-success: linear-gradient(135deg, #28a745 0%, #1c7430 100%);
  --gradient-btn-danger: linear-gradient(135deg, #ff5733 0%, #c82333 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(245, 245, 245, 0.8) 100%
  );
  --gradient-sidebar: linear-gradient(180deg, #333333 0%, #1e90ff 100%);

  /* Typography */
  --font-primary: "Montserrat", sans-serif;
  --font-secondary: "Poppins", sans-serif;
  --font-mono: "Consolas", monospace;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 4rem;

  /* Borders & Shadows */
  --border-radius-sm: 6px;
  --border-radius-md: 12px;
  --border-radius-lg: 20px;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.15);
  --box-shadow-btn: 0 4px 10px rgba(7, 122, 125, 0.25);
  --box-shadow-btn-hover: 0 8px 20px rgba(7, 122, 125, 0.35);

  /* Transitions */
  --transition-fast: 0.3s ease;
  --transition-medium: 0.5s ease;
  --transition-slow: 0.8s ease;

  /* Layout */
  --sidebar-width: 250px;
  --topbar-height: 70px;
}

/* Dashboard Background with Animated Gradient */
.dashboard-body {
  min-height: 100vh;
  position: relative;
  font-family: var(--font-secondary);
  color: var(--dark);
  background-color: var(--light);
  padding-top: var(--topbar-height);
}

/* Animated Background */
.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(234, 234, 234, 0.95) 0%,
    rgba(245, 238, 221, 0.95) 100%
  );
  z-index: -1;
}

.animated-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  opacity: 0.5;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background-color: var(--primary-light);
  opacity: 0.2;
  pointer-events: none;
}

/* Topbar Styling */
#topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--topbar-height);
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1030;
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  transition: all var(--transition-fast);
}

#topbar .navbar-brand {
  display: flex;
  align-items: center;
  color: var(--primary);
  font-weight: 700;
  font-size: 1.3rem;
  text-decoration: none;
  transition: all var(--transition-fast);
}

#topbar .navbar-brand i {
  color: var(--primary);
  margin-right: 0.5rem;
  font-size: 1.5rem;
  transition: all var(--transition-fast);
  filter: drop-shadow(0 0 5px rgba(122, 226, 207, 0.5));
}

#topbar .navbar-brand:hover {
  transform: translateY(-2px);
}

#topbar .navbar-brand:hover i {
  transform: scale(1.1) rotate(5deg);
  color: var(--primary-light);
}

.topbar-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 600;
  font-size: 1rem;
}

.user-name {
  font-weight: 600;
  color: var(--dark);
  font-size: 0.9rem;
}

.btn-logout {
  background: transparent;
  color: var(--danger);
  border: 1px solid var(--danger);
  border-radius: var(--border-radius-md);
  padding: 0.4rem 0.9rem;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all var(--transition-fast);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.btn-logout:hover {
  background: var(--danger);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(220, 53, 69, 0.2);
}

.btn-logout i {
  margin-right: 0.4rem;
  font-size: 0.9rem;
}

/* Sidebar Styling */
.sidebar {
  position: fixed;
  top: var(--topbar-height);
  left: 0;
  bottom: 0;
  width: var(--sidebar-width);
  background: var(--white);
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  z-index: 1020;
  transition: transform var(--transition-fast);
  overflow-y: auto;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  padding: 0.9rem 1.5rem;
  color: var(--gray);
  font-weight: 500;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.sidebar .nav-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(122, 226, 207, 0.1),
    transparent
  );
  transition: all 0.6s ease;
  z-index: -1;
}

.sidebar .nav-link:hover {
  color: var(--primary);
  background-color: rgba(122, 226, 207, 0.05);
  border-left-color: var(--primary-light);
}

.sidebar .nav-link:hover::before {
  left: 100%;
}

.sidebar .nav-link.active {
  color: var(--white);
  background: var(--gradient-btn-primary);
  border-left-color: var(--primary);
  font-weight: 600;
  box-shadow: var(--box-shadow-btn);
}

.sidebar .nav-link i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
  transition: all var(--transition-fast);
}

.sidebar .nav-link:hover i {
  transform: translateX(3px);
}

.sidebar .nav-link .badge {
  margin-left: auto;
  background-color: var(--primary);
  color: var(--white);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.35rem 0.65rem;
  border-radius: 50px;
  transition: all var(--transition-fast);
}

.sidebar .nav-link:hover .badge {
  transform: scale(1.1);
  background-color: var(--primary-dark);
}

.sidebar .nav-link.active .badge {
  background-color: var(--white);
  color: var(--primary);
}

.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1015;
  backdrop-filter: blur(3px);
}

/* Main Content Area */
.content {
  margin-left: var(--sidebar-width);
  padding: 2rem;
  transition: margin var(--transition-fast);
}

.sb-sidenav-toggled .sidebar {
  transform: translateX(-100%);
}

.sb-sidenav-toggled .content {
  margin-left: 0;
}

.sb-sidenav-toggled .sidebar-overlay {
  display: block;
}

/* Dashboard Cards */
.dashboard-card {
  background: var(--neutral);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: transform var(--transition-medium),
    box-shadow var(--transition-medium);
  height: auto;
  max-height: fit-content;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--dark);
  font-size: 1.8rem;
}

.card-body {
  padding: 1.25rem;
  background-color: var(--white);
}

/* Agent Control Styling */
.agent-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#status-badge {
  padding: 0.4rem 0.8rem;
  font-weight: 500;
  font-size: 0.85rem;
  border-radius: 50px;
}

#status-badge.bg-success {
  background-color: var(--success) !important;
  box-shadow: 0 2px 8px rgba(45, 170, 158, 0.3);
  animation: pulse 2s infinite;
}

#status-badge.bg-danger {
  background-color: var(--danger) !important;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

#status-badge.bg-warning {
  background-color: var(--warning) !important;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

#status-time {
  font-size: 0.75rem;
  color: var(--gray);
}

/* Action Buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem 1.5rem;
  font-weight: 500;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  border: 2px solid transparent;
  cursor: pointer;
  text-decoration: none;
  gap: 0.5rem;
  box-shadow: var(--box-shadow-md);
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--box-shadow-lg);
}

.action-btn:active {
  transform: scale(1.05);
}

.action-btn i {
  font-size: 1.1rem;
}

.btn-primary {
  background: var(--gradient-btn-primary);
  color: var(--white);
  border-color: var(--white);
}

.btn-primary:hover {
  border-color: var(--accent);
  color: var(--white);
}

.btn-success {
  background: var(--gradient-btn-success);
  color: var(--white);
  border-color: var(--white);
}

.btn-success:hover {
  border-color: var(--accent);
  color: var(--white);
}

.btn-danger {
  background: var(--gradient-btn-danger);
  color: var(--white);
  border-color: var(--white);
}

.btn-danger:hover {
  border-color: var(--accent);
  color: var(--white);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-outline-primary:hover {
  background-color: rgba(30, 144, 255, 0.1);
  color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-outline-success {
  background-color: transparent;
  color: var(--success);
  border: 2px solid var(--success);
}

.btn-outline-success:hover {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success);
  border-color: var(--success);
}

.btn-outline-danger {
  background-color: transparent;
  color: var(--danger);
  border: 2px solid var(--danger);
}

.btn-outline-danger:hover {
  background-color: rgba(255, 87, 51, 0.1);
  color: var(--danger);
  border-color: var(--danger);
}

/* Stats Cards */
.stat-card {
  background: var(--white);
  border-radius: var(--border-radius-md);
  padding: 1.25rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all var(--transition-medium);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card .d-flex {
  margin-bottom: 1rem;
}

.stat-card i {
  color: var(--primary);
  font-size: 1.5rem;
  margin-right: 0.75rem;
  transition: all var(--transition-fast);
}

.stat-card:hover i {
  transform: scale(1.2) rotate(10deg);
}

.stat-card h6 {
  margin: 0;
  font-weight: 500;
  color: var(--gray);
  font-size: 0.9rem;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark);
  margin-top: auto;
  text-align: center;
}

/* Timeline Styling */
.timeline {
  position: relative;
  padding-left: 1.5rem;
}

.timeline-item {
  position: relative;
  padding-bottom: 1.25rem;
  padding-left: 1rem;
  border-left: 2px solid var(--light);
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -0.5rem;
  top: 0;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: var(--primary-light);
  border: 2px solid var(--white);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.timeline-content {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.timeline-time {
  font-size: 0.75rem;
  color: var(--gray);
  margin-bottom: 0.25rem;
}

.timeline-message {
  font-size: 0.9rem;
  color: var(--dark);
  margin: 0;
}

/* Logs Panel Styling */
.logs-panel {
  height: 400px;
  overflow-y: auto;
  background-color: var(--neutral);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
  font-family: var(--font-mono);
  scrollbar-width: thin;
  scrollbar-color: var(--primary) var(--neutral);
}

.logs-panel::-webkit-scrollbar {
  width: 8px;
}

.logs-panel::-webkit-scrollbar-track {
  background: var(--neutral);
  border-radius: 8px;
}

.logs-panel::-webkit-scrollbar-thumb {
  background-color: var(--primary);
  border-radius: 8px;
  border: 2px solid var(--neutral);
}

.log-entry {
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-left: 3px solid var(--gray-light);
  border-radius: 8px;
  background-color: var(--white);
  font-size: 1rem;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
  animation: fadeIn var(--transition-medium) forwards;
}

.log-entry:hover {
  transform: translateX(5px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.log-success {
  border-left-color: var(--success);
}

.log-error {
  border-left-color: var(--danger);
  background-color: rgba(255, 87, 51, 0.05);
}

.log-timestamp {
  color: var(--gray);
  font-size: 0.75rem;
  margin-right: 0.5rem;
  font-weight: 500;
}

.log-message {
  color: var(--dark);
  font-weight: 500;
  font-size: 1rem;
}

/* Email List Styling */
.email-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.email-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: var(--border-radius-md);
  background-color: var(--white);
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all var(--transition-fast);
}

.email-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.email-item-content {
  flex: 1;
}

.email-address {
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 0.25rem;
}

.email-actions {
  display: flex;
  gap: 0.5rem;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1100;
  max-width: 350px;
}

.toast {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  margin-bottom: 10px;
}

.toast-header {
  background-color: var(--primary);
  color: var(--white);
  border-bottom: none;
}

.toast-body {
  padding: 1rem;
  font-size: 0.9rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(45, 170, 158, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(45, 170, 158, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(45, 170, 158, 0);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

.fade-in {
  animation: fadeIn var(--transition-medium) forwards;
}

.slide-up {
  animation: slideUp var(--transition-medium) forwards;
}

/* Animation Delays */
.delay-1 {
  animation-delay: 0.1s;
}

.delay-2 {
  animation-delay: 0.2s;
}

.delay-3 {
  animation-delay: 0.3s;
}

.delay-4 {
  animation-delay: 0.4s;
}

.delay-5 {
  animation-delay: 0.5s;
}

/* Agent Loading Animation */
.agent-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-medium);
}

.agent-loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.agent-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  max-width: 90%;
  width: 400px;
  text-align: center;
  transform: translateY(20px);
  transition: transform var(--transition-medium);
}

.agent-loading-overlay.show .agent-loading-container {
  transform: translateY(0);
}

.agent-loading-spinner {
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
  position: relative;
}

.agent-loading-spinner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid rgba(122, 226, 207, 0.2);
}

.agent-loading-spinner::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid transparent;
  border-top-color: var(--primary);
  animation: rotate 1.5s linear infinite;
}

.agent-loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: var(--primary);
  animation: bounce 2s ease infinite;
}

.agent-loading-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 0.5rem;
}

.agent-loading-subtext {
  color: var(--gray);
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

/* Email Preview Styles */
.email-preview-container {
  margin-bottom: 2rem;
}

.email-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.email-card {
  background: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: transform var(--transition-medium),
    box-shadow var(--transition-medium);
}

.email-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.email-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.email-header h3 {
  margin: 0 0 0.75rem 0;
  font-weight: 600;
  color: var(--dark);
  font-size: 1.2rem;
}

.email-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.email-meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.email-meta-label {
  font-weight: 600;
  color: var(--gray);
  font-size: 0.9rem;
}

.email-meta-value {
  color: var(--dark);
  font-size: 0.9rem;
}

.email-body {
  padding: 1.5rem;
  background-color: var(--light);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.95rem;
  line-height: 1.6;
}

.reply-section {
  padding: 1.5rem;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.reply-header h4 {
  margin: 0;
  font-weight: 600;
  color: var(--dark);
  font-size: 1.1rem;
}

.reply-status {
  padding: 0.3rem 0.75rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
}

.reply-status-pending {
  background-color: var(--warning);
  color: var(--dark);
}

.reply-status-sent {
  background-color: var(--success);
  color: var(--white);
}

.reply-status-error {
  background-color: var(--danger);
  color: var(--white);
}

.reply-content {
  background-color: var(--light);
  border-radius: var(--border-radius-md);
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 0.95rem;
  line-height: 1.6;
}

.reply-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.no-reply-needed {
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: var(--border-radius-md);
  padding: 1.25rem;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.no-reply-needed-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.no-reply-needed-header i {
  color: var(--warning);
  font-size: 1.5rem;
}

.no-reply-needed-header h4 {
  margin: 0;
  font-weight: 600;
  color: var(--dark);
  font-size: 1.1rem;
}

.no-reply-needed-message {
  margin-bottom: 1.25rem;
  color: var(--gray);
}

/* Settings Page Styles */
.settings-option-group {
  background-color: var(--neutral);
  border-radius: 8px;
  padding: 1.25rem;
  position: relative;
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.pro-feature-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background-color: var(--primary);
  color: var(--white);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 2px 8px rgba(30, 144, 255, 0.3);
}

.pro-feature-badge i {
  color: var(--warning);
}

.form-text {
  margin-top: 0.5rem;
  color: var(--gray);
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.form-text i {
  color: var(--primary);
}

.form-control {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  transition: all var(--transition-fast);
}

.form-control:focus {
  border-color: var(--accent);
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.input-group-text {
  background-color: var(--neutral);
  border: 1px solid var(--border-color);
  border-radius: 0 8px 8px 0;
  color: var(--gray);
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.25rem;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.form-check-input:checked {
  background-color: var(--accent);
  border-color: var(--accent);
}

.form-check-input:focus {
  border-color: var(--accent);
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-check-label {
  padding-left: 0.5rem;
  color: var(--dark);
}

.plan-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.plan-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-btn-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  box-shadow: 0 5px 15px rgba(30, 144, 255, 0.3);
}

.plan-icon i {
  font-size: 1.75rem;
  color: var(--white);
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: 0.1rem;
}

.plan-limit {
  color: var(--gray);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.plan-features {
  width: 100%;
  margin-bottom: 1rem;
}

.plan-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.plan-feature:last-child {
  border-bottom: none;
}

.plan-feature i {
  color: var(--accent);
  font-size: 1rem;
}

.plan-feature span {
  font-size: 0.85rem;
}

.plan-feature.disabled i {
  color: var(--danger);
}

.plan-feature.disabled span {
  color: var(--gray);
}

.account-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-bottom: 0.5rem;
}

/* Email Editor Modal */
.email-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-medium);
}

.email-editor-modal.show {
  opacity: 1;
  visibility: visible;
}

.email-editor-container {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(20px);
  transition: transform var(--transition-medium);
}

.email-editor-modal.show .email-editor-container {
  transform: translateY(0);
}

.email-editor-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.email-editor-header h4 {
  margin: 0;
  font-weight: 600;
  color: var(--dark);
  font-size: 1.2rem;
}

.email-editor-close {
  background: transparent;
  border: none;
  color: var(--gray);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.email-editor-close:hover {
  color: var(--danger);
  transform: scale(1.1);
}

.email-editor-body {
  padding: 1.5rem;
}

.email-editor-textarea {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-md);
  font-family: var(--font-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
  resize: vertical;
  transition: all var(--transition-fast);
}

.email-editor-textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(7, 122, 125, 0.1);
}

.email-editor-footer {
  padding: 1.25rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Regenerate Modal */
.regenerate-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-medium);
}

.regenerate-modal.show {
  opacity: 1;
  visibility: visible;
}

.regenerate-container {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(20px);
  transition: transform var(--transition-medium);
}

.regenerate-modal.show .regenerate-container {
  transform: translateY(0);
}

.regenerate-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.regenerate-header h4 {
  margin: 0;
  font-weight: 600;
  color: var(--dark);
  font-size: 1.2rem;
}

.regenerate-close {
  background: transparent;
  border: none;
  color: var(--gray);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.regenerate-close:hover {
  color: var(--danger);
  transform: scale(1.1);
}

.regenerate-body {
  padding: 1.5rem;
}

.regenerate-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.regenerate-option {
  background-color: var(--light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: var(--border-radius-md);
  padding: 1.25rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.regenerate-option:hover {
  background-color: rgba(122, 226, 207, 0.1);
  border-color: var(--primary-light);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.regenerate-option.selected {
  background-color: rgba(122, 226, 207, 0.2);
  border-color: var(--primary);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.regenerate-option-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.regenerate-option-header i {
  color: var(--primary);
  font-size: 1.25rem;
}

.regenerate-option-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--dark);
  font-size: 1rem;
}

.regenerate-option-description {
  color: var(--gray);
  font-size: 0.9rem;
  margin: 0;
}

.regenerate-footer {
  padding: 1.25rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Profile Page Styles */
.profile-card {
  background: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform var(--transition-medium),
    box-shadow var(--transition-medium);
  height: auto;
  max-height: fit-content;
}

.profile-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.profile-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid rgba(122, 226, 207, 0.2);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-medium);
}

.profile-card:hover .profile-image {
  transform: scale(1.05);
  border-color: rgba(122, 226, 207, 0.4);
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark);
  margin: 1rem 0 0.5rem;
}

.profile-email {
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.profile-info-row {
  display: flex;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.profile-info-label {
  width: 30%;
  font-weight: 600;
  color: var(--dark);
  font-size: 0.9rem;
}

.profile-info-value {
  width: 70%;
  color: var(--gray);
  font-size: 0.9rem;
}

.connected-service {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background-color: var(--light);
  border-radius: var(--border-radius-md);
  margin-bottom: 0.75rem;
  transition: all var(--transition-fast);
}

.connected-service:hover {
  background-color: rgba(122, 226, 207, 0.1);
  transform: translateX(5px);
}

.connected-service-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.connected-service-icon i {
  color: var(--primary);
  font-size: 1rem;
}

.connected-service-info {
  flex: 1;
}

.connected-service-name {
  font-weight: 600;
  color: var(--dark);
  margin: 0 0 0.1rem 0;
  font-size: 0.9rem;
}

.connected-service-email {
  color: var(--gray);
  font-size: 0.8rem;
  margin: 0;
}

.connected-service-status {
  padding: 0.15rem 0.5rem;
  border-radius: 50px;
  font-size: 0.7rem;
  font-weight: 500;
  background-color: var(--success);
  color: var(--white);
}

/* Settings Page Styles */
.settings-card {
  background: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform var(--transition-medium),
    box-shadow var(--transition-medium);
  height: 100%;
  margin-bottom: 1.5rem;
}

.settings-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.settings-section {
  margin-bottom: 2rem;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 0.5rem;
  display: block;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--dark);
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
}

.form-control:focus {
  color: var(--dark);
  background-color: var(--white);
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(7, 122, 125, 0.1);
}

.form-text {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--gray);
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.form-check:last-child {
  margin-bottom: 0;
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-label {
  font-weight: 500;
  color: var(--dark);
  cursor: pointer;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--gray);
  text-align: center;
  white-space: nowrap;
  background-color: var(--light);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-md);
}

.input-group > .form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

.input-group:not(.has-validation)
  > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3),
.input-group:not(.has-validation)
  > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group
  > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
    .valid-feedback
  ):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.danger-zone {
  background-color: rgba(220, 53, 69, 0.05);
  border: 1px solid rgba(220, 53, 69, 0.1);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  margin-top: 2rem;
}

.danger-zone-title {
  color: var(--danger);
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.danger-zone-title i {
  font-size: 1.25rem;
}

.danger-zone-description {
  color: var(--gray);
  margin-bottom: 1.5rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
  }

  .content {
    margin-left: 0;
    padding: 1.5rem;
  }

  .sb-sidenav-toggled .sidebar {
    transform: translateX(0);
  }

  .reply-actions {
    flex-direction: column;
  }

  .reply-actions .action-btn {
    width: 100%;
  }

  .profile-info-row {
    flex-direction: column;
  }

  .profile-info-label,
  .profile-info-value {
    width: 100%;
  }

  .profile-info-label {
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-header h5 {
    margin-bottom: 0.75rem;
  }

  .agent-status {
    width: 100%;
    justify-content: space-between;
  }

  .stat-card {
    margin-bottom: 1rem;
  }

  .stat-value {
    font-size: 2rem;
  }

  .logs-panel {
    height: 300px;
  }

  .email-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .email-meta-item {
    width: 100%;
  }

  .email-editor-footer,
  .regenerate-footer {
    flex-direction: column;
  }

  .email-editor-footer .action-btn,
  .regenerate-footer .action-btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .content {
    padding: 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .action-btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .logs-panel {
    height: 250px;
  }

  .stat-value {
    font-size: 1.75rem;
  }

  #topbar {
    padding: 0 1rem;
  }

  .user-info {
    display: none;
  }

  .email-header h3 {
    font-size: 1.1rem;
  }

  .reply-header h4 {
    font-size: 1rem;
  }

  .profile-image {
    width: 120px;
    height: 120px;
  }

  .profile-name {
    font-size: 1.3rem;
  }
}
