"""
Database Viewer for Render PostgreSQL

This script provides a simple way to view data stored in your Render PostgreSQL database.
It allows you to list tables, view table contents, and execute custom SQL queries.

Usage:
    python view_database.py [command] [arguments]

Commands:
    list_tables              - List all tables in the database
    view_table [table_name]  - View all records in a specific table
    count_records            - Count records in all tables
    query [sql_query]        - Execute a custom SQL query
    help                     - Show this help message

Examples:
    python view_database.py list_tables
    python view_database.py view_table user
    python view_database.py count_records
    python view_database.py query "SELECT * FROM user LIMIT 5"
"""
import os
import sys
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv
from tabulate import tabulate
import textwrap

# Load environment variables from .env file if it exists
load_dotenv()

def get_database_connection():
    """Connect to the Render PostgreSQL database"""
    # Get database URL from environment
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        print("Please set the DATABASE_URL environment variable to your Render PostgreSQL URL")
        sys.exit(1)
    
    # Hide password in logs
    masked_url = database_url
    if '@' in database_url:
        parts = database_url.split('@')
        auth_part = parts[0].split('://')
        if len(auth_part) > 1:
            user_pass = auth_part[1].split(':')
            if len(user_pass) > 1:
                masked_url = f"{auth_part[0]}://{user_pass[0]}:****@{parts[1]}"
    
    print(f"Connecting to database: {masked_url}")
    
    try:
        # Parse the connection string
        if '?' in database_url:
            # Remove query parameters for psycopg2
            conn_string = database_url.split('?')[0]
        else:
            conn_string = database_url
        
        # Connect to the database
        conn = psycopg2.connect(conn_string)
        print("Database connection successful!")
        return conn
    except Exception as e:
        print(f"Error connecting to database: {e}")
        sys.exit(1)

def list_tables(conn):
    """List all tables in the database"""
    try:
        cur = conn.cursor()
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        tables = cur.fetchall()
        
        if not tables:
            print("No tables found in the database.")
            return
        
        print("\n=== Tables in Database ===")
        for i, (table,) in enumerate(tables, 1):
            print(f"{i}. {table}")
        print(f"\nTotal tables: {len(tables)}")
        
        cur.close()
    except Exception as e:
        print(f"Error listing tables: {e}")

def view_table(conn, table_name, limit=100):
    """View all records in a specific table"""
    try:
        # Validate table name to prevent SQL injection
        cur = conn.cursor()
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            )
        """, (table_name,))
        
        if not cur.fetchone()[0]:
            print(f"Table '{table_name}' does not exist.")
            cur.close()
            return
        
        # Get column names
        cur.execute(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = %s
            ORDER BY ordinal_position
        """, (table_name,))
        
        columns = cur.fetchall()
        column_names = [col[0] for col in columns]
        
        # Get data with column names
        cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cur.execute(f"SELECT * FROM \"{table_name}\" LIMIT %s", (limit,))
        records = cur.fetchall()
        
        if not records:
            print(f"No records found in table '{table_name}'.")
            return
        
        # Format data for display
        formatted_data = []
        for record in records:
            row = []
            for col in column_names:
                value = record[col]
                # Format value for display
                if value is None:
                    row.append("NULL")
                elif isinstance(value, str) and len(value) > 50:
                    row.append(textwrap.shorten(value, width=50, placeholder="..."))
                else:
                    row.append(str(value))
            formatted_data.append(row)
        
        print(f"\n=== Records in '{table_name}' (Limited to {limit}) ===")
        print(tabulate(formatted_data, headers=column_names, tablefmt="grid"))
        print(f"\nShowing {len(records)} of {count_table_records(conn, table_name)} records")
        
        cur.close()
    except Exception as e:
        print(f"Error viewing table: {e}")

def count_table_records(conn, table_name):
    """Count records in a specific table"""
    try:
        cur = conn.cursor()
        cur.execute(f'SELECT COUNT(*) FROM "{table_name}"')
        count = cur.fetchone()[0]
        cur.close()
        return count
    except Exception as e:
        print(f"Error counting records in {table_name}: {e}")
        return 0

def count_all_records(conn):
    """Count records in all tables"""
    try:
        cur = conn.cursor()
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        tables = cur.fetchall()
        
        if not tables:
            print("No tables found in the database.")
            return
        
        print("\n=== Record Counts ===")
        total_records = 0
        
        for table, in tables:
            count = count_table_records(conn, table)
            total_records += count
            print(f"{table}: {count} records")
        
        print(f"\nTotal records across all tables: {total_records}")
        
        cur.close()
    except Exception as e:
        print(f"Error counting records: {e}")

def execute_query(conn, query):
    """Execute a custom SQL query"""
    try:
        cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cur.execute(query)
        
        # Check if the query returns data
        if cur.description:
            records = cur.fetchall()
            if not records:
                print("Query executed successfully. No records returned.")
                return
            
            # Get column names
            column_names = [desc[0] for desc in cur.description]
            
            # Format data for display
            formatted_data = []
            for record in records:
                row = []
                for i, col in enumerate(column_names):
                    value = record[i]
                    # Format value for display
                    if value is None:
                        row.append("NULL")
                    elif isinstance(value, str) and len(value) > 50:
                        row.append(textwrap.shorten(value, width=50, placeholder="..."))
                    else:
                        row.append(str(value))
                formatted_data.append(row)
            
            print("\n=== Query Results ===")
            print(tabulate(formatted_data, headers=column_names, tablefmt="grid"))
            print(f"\nReturned {len(records)} records")
        else:
            # For non-SELECT queries
            conn.commit()
            print(f"Query executed successfully. Rows affected: {cur.rowcount}")
        
        cur.close()
    except Exception as e:
        print(f"Error executing query: {e}")
        conn.rollback()

def show_help():
    """Show help message"""
    print(__doc__)

def main():
    """Main function to handle command-line arguments"""
    # Check if tabulate is installed
    try:
        import tabulate
    except ImportError:
        print("The 'tabulate' package is required for this script.")
        print("Please install it using: pip install tabulate")
        sys.exit(1)
    
    # Parse command-line arguments
    if len(sys.argv) < 2 or sys.argv[1] == 'help':
        show_help()
        sys.exit(0)
    
    command = sys.argv[1].lower()
    
    # Connect to the database
    conn = get_database_connection()
    
    try:
        if command == 'list_tables':
            list_tables(conn)
        
        elif command == 'view_table':
            if len(sys.argv) < 3:
                print("Error: Missing table name")
                print("Usage: python view_database.py view_table [table_name]")
                sys.exit(1)
            
            table_name = sys.argv[2]
            limit = 100  # Default limit
            
            # Check if limit is provided
            if len(sys.argv) > 3:
                try:
                    limit = int(sys.argv[3])
                except ValueError:
                    print(f"Invalid limit: {sys.argv[3]}. Using default limit of 100.")
            
            view_table(conn, table_name, limit)
        
        elif command == 'count_records':
            count_all_records(conn)
        
        elif command == 'query':
            if len(sys.argv) < 3:
                print("Error: Missing SQL query")
                print("Usage: python view_database.py query \"[sql_query]\"")
                sys.exit(1)
            
            query = sys.argv[2]
            execute_query(conn, query)
        
        else:
            print(f"Unknown command: {command}")
            show_help()
    
    finally:
        # Close the database connection
        if conn:
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    main()
