"""
<PERSON><PERSON><PERSON> to update psycopg2-binary to the latest version.
This script runs automatically during the Render build process.
"""
import subprocess
import sys
import os
import time

def update_psycopg2():
    """Update psycopg2-binary to version 2.9.9"""
    print("=" * 80)
    print("PSYCOPG2 UPDATE SCRIPT")
    print("=" * 80)
    print("Checking and updating psycopg2-binary to version 2.9.9...")

    # Check if we're in a Render environment
    is_render = os.environ.get('RENDER') == 'true'
    if is_render:
        print("Running in Render production environment")
    else:
        print("Running in local/development environment")

    try:
        # Check current version
        try:
            result = subprocess.check_output([sys.executable, "-m", "pip", "show", "psycopg2-binary"])
            current_version = None
            for line in result.decode('utf-8').splitlines():
                if line.startswith('Version:'):
                    current_version = line.split(':', 1)[1].strip()
                    break

            if current_version:
                print(f"Current psycopg2-binary version: {current_version}")
                if current_version == '2.9.9':
                    print("Already using the correct version. No update needed.")
                    return True
            else:
                print("Could not determine current psycopg2-binary version")
        except subprocess.CalledProcessError:
            print("psycopg2-binary not currently installed")

        # Uninstall current version (if any)
        print("Uninstalling current psycopg2-binary version...")
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "-y", "psycopg2-binary"])
        print("Successfully uninstalled current psycopg2-binary version")

        # Install new version with retries
        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                print(f"Installing psycopg2-binary 2.9.9 (attempt {attempt}/{max_retries})...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "psycopg2-binary==2.9.9"])
                print("Successfully installed psycopg2-binary 2.9.9")
                break
            except subprocess.CalledProcessError as e:
                print(f"Error installing psycopg2-binary (attempt {attempt}/{max_retries}): {e}")
                if attempt < max_retries:
                    print(f"Retrying in 5 seconds...")
                    time.sleep(5)
                else:
                    print("All installation attempts failed.")
                    return False

        # Verify installation
        result = subprocess.check_output([sys.executable, "-m", "pip", "show", "psycopg2-binary"])
        print(f"Installed package info:\n{result.decode('utf-8')}")

        print("=" * 80)
        print("PSYCOPG2 UPDATE COMPLETED SUCCESSFULLY")
        print("=" * 80)
        return True
    except Exception as e:
        print(f"Unexpected error updating psycopg2-binary: {e}")
        return False

if __name__ == "__main__":
    success = update_psycopg2()
    if not success:
        sys.exit(1)  # Exit with error code
