"""
<PERSON><PERSON><PERSON> to create a database migration for changing the OAuthToken model.
"""
from app import app
from models import db
from flask_migrate import Migrate, migrate, upgrade

def create_migration():
    """Create a migration to change the OAuthToken model"""
    print("Creating migration for changing OAuthToken model...")
    
    # Initialize Flask-Migrate
    migrate_instance = Migrate(app, db)
    
    with app.app_context():
        # Create a migration
        migrate(message="Change OAuthToken model to use user_id as primary key")
        print("Migration created")
        
        # Apply the migration
        upgrade()
        print("Migration applied")
        
        print("Migration completed successfully!")

if __name__ == "__main__":
    create_migration()
